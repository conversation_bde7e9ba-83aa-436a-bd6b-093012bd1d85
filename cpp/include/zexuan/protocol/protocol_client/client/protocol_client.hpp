/**
 * @file protocol_client.hpp  
 * @brief IEC 60870-5-103 协议客户端接口定义
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_PROTOCOL_CLIENT_HPP
#define ZEXUAN_PROTOCOL_CLIENT_PROTOCOL_CLIENT_HPP

#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

#include "zexuan/base/base_types.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/protocol/protocol_client/message/message_type_handler.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 客户端配置结构
     */
    struct ClientConfig {
        std::string server_host = "127.0.0.1";  ///< 服务器地址
        uint16_t server_port = 8080;             ///< 服务器端口
        uint32_t connect_timeout_ms = 5000;      ///< 连接超时（毫秒）
        uint32_t reconnect_interval_ms = 3000;   ///< 重连间隔（毫秒）
        uint32_t max_reconnect_attempts = 10;    ///< 最大重连次数
        bool enable_auto_reconnect = true;       ///< 是否启用自动重连
        uint32_t send_timeout_ms = 3000;         ///< 发送超时（毫秒）
        
        /**
         * @brief 从配置文件加载配置
         * @param config_file_path 配置文件路径
         * @return 加载结果
         */
        bool LoadFromFile(const std::string& config_file_path);
    };

    /**
     * @brief 连接状态枚举
     */
    enum class ConnectionState {
        DISCONNECTED = 0,  ///< 未连接
        CONNECTING,        ///< 连接中
        CONNECTED,         ///< 已连接
        RECONNECTING,      ///< 重连中
        ERROR             ///< 连接错误
    };

    /**
     * @brief 连接状态回调函数
     * @param state 新的连接状态
     * @param description 状态描述信息
     */
    using ConnectionStateCallback = std::function<void(ConnectionState state, const std::string& description)>;

    /**
     * @brief 消息响应回调函数
     * @param message_type 消息类型
     * @param result 处理结果
     */
    using MessageResponseCallback = std::function<void(uint8_t message_type, const MessageResult& result)>;

    /**
     * @brief 原始数据接收回调函数类型
     * @param data 接收到的数据
     * @param size 数据大小
     */
    using RawDataReceivedCallback = std::function<void(const uint8_t* data, size_t size)>;

    /**
     * @brief IEC 60870-5-103 协议客户端接口
     * 
     * 提供协议客户端的核心功能：
     * 1. 连接管理（连接、断开、重连）
     * 2. 消息发送（支持Type 1-255）
     * 3. 响应处理（自动分发到对应处理器）
     * 4. 处理器管理（注册、查询）
     * 5. 状态监控和回调
     */
    class IProtocolClient {
    public:
        virtual ~IProtocolClient() = default;

        /**
         * @brief 启动客户端
         * @return 启动结果
         */
        virtual bool Start() = 0;

        /**
         * @brief 停止客户端
         * @return 停止结果
         */
        virtual bool Stop() = 0;

        /**
         * @brief 检查客户端是否正在运行
         * @return true:运行中 false:已停止
         */
        virtual bool IsRunning() const = 0;

        /**
         * @brief 检查是否已连接到服务器
         * @return true:已连接 false:未连接
         */
        virtual bool IsConnected() const = 0;

        /**
         * @brief 获取当前连接状态
         * @return 连接状态
         */
        virtual ConnectionState GetConnectionState() const = 0;

        /**
         * @brief 手动发起连接
         * @return 连接结果
         */
        virtual bool Connect() = 0;

        /**
         * @brief 断开连接
         */
        virtual void Disconnect() = 0;

        /**
         * @brief 发送消息
         * @param message_type 消息类型 (1-255)
         * @param request 消息请求参数
         * @return 发送结果
         */
        virtual base::Result<void> SendMessage(uint8_t message_type, const MessageRequest& request) = 0;

        /**
         * @brief 发送原始字符串消息（兼容现有接口）
         * @param message 原始消息字符串
         * @return 发送结果
         */
        virtual base::Result<void> SendRawMessage(const std::string& message) = 0;

        /**
         * @brief 注册消息类型处理器
         * @param handler 消息处理器
         * @return 注册结果
         */
        virtual bool RegisterMessageHandler(std::unique_ptr<IMessageTypeHandler> handler) = 0;

        /**
         * @brief 注销消息类型处理器
         * @param message_type 消息类型
         * @return 注销结果
         */
        virtual bool UnregisterMessageHandler(uint8_t message_type) = 0;

        /**
         * @brief 检查是否支持指定消息类型
         * @param message_type 消息类型
         * @return true:支持 false:不支持
         */
        virtual bool SupportMessageType(uint8_t message_type) const = 0;

        /**
         * @brief 获取支持的消息类型列表
         * @return 消息类型列表
         */
        virtual std::vector<uint8_t> GetSupportedMessageTypes() const = 0;

        /**
         * @brief 设置连接状态变化回调
         * @param callback 回调函数
         */
        virtual void SetConnectionStateCallback(ConnectionStateCallback callback) = 0;

        /**
         * @brief 设置消息响应回调
         * @param callback 回调函数
         */
        virtual void SetMessageResponseCallback(MessageResponseCallback callback) = 0;

        /**
         * @brief 设置原始数据接收回调
         * @param callback 回调函数
         */
        virtual void SetRawDataReceivedCallback(RawDataReceivedCallback callback) = 0;

        /**
         * @brief 获取客户端配置
         * @return 客户端配置
         */
        virtual const ClientConfig& GetConfig() const = 0;

        /**
         * @brief 获取客户端统计信息
         */
        struct Statistics {
            uint64_t messages_sent = 0;
            uint64_t messages_received = 0;
            uint64_t bytes_received = 0;
            uint64_t connection_attempts = 0;
            uint64_t reconnection_count = 0;
            uint32_t registered_handlers = 0;
        };
        virtual Statistics GetStatistics() const = 0;
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_PROTOCOL_CLIENT_HPP
